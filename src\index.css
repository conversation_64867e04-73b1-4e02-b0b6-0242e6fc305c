@tailwind base;
@tailwind components;
@tailwind utilities;

/* xSpecies AI Design System - Next-Gen Futuristic UI */

@layer base {
  :root {
    /* Professional corporate theme - Light mode primary */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Professional blue primary colors */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 221 83% 63%;
    --primary-dark: 221 83% 43%;

    /* Professional secondary colors */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 84% 4.9%;

    /* Professional muted colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    /* Professional accent colors */
    --accent: 210 40% 96%;
    --accent-foreground: 222 84% 4.9%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    /* Professional design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(221 83% 53%), hsl(213 94% 68%));
    --gradient-secondary: linear-gradient(135deg, hsl(210 40% 96%), hsl(214 32% 91%));
    --gradient-hero: linear-gradient(135deg, hsl(221 83% 53% / 0.08), hsl(213 94% 68% / 0.04));
    --gradient-mesh: radial-gradient(circle at 20% 80%, hsl(221 83% 53% / 0.06) 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, hsl(213 94% 68% / 0.06) 0%, transparent 50%),
                     radial-gradient(circle at 40% 40%, hsl(204 94% 78% / 0.04) 0%, transparent 50%);
    --gradient-radial: radial-gradient(circle, var(--primary) 0%, transparent 70%);

    /* Professional shadows */
    --shadow-glow: 0 0 30px hsl(221 83% 53% / 0.15);
    --shadow-glow-lg: 0 0 50px hsl(221 83% 53% / 0.12);
    --shadow-card: 0 4px 20px hsl(222 84% 4.9% / 0.08);
    --shadow-elegant: 0 8px 40px hsl(222 84% 4.9% / 0.12);
    --shadow-professional: 0 2px 10px hsl(221 83% 53% / 0.1), 0 4px 20px hsl(221 83% 53% / 0.06);

    /* Advanced transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Humanoid Black Theme - Deep space black with robotic cyan accents */
    --background: 0 0% 3%;
    --foreground: 0 0% 95%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 95%;

    /* Humanoid cyan primary - robotic/AI aesthetic */
    --primary: 180 100% 50%;
    --primary-foreground: 0 0% 3%;
    --primary-glow: 180 100% 60%;
    --primary-dark: 180 100% 40%;

    /* Humanoid secondary - dark grays with subtle warmth */
    --secondary: 0 0% 12%;
    --secondary-foreground: 0 0% 90%;

    /* Humanoid muted - charcoal grays */
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 65%;

    /* Humanoid accent - slightly lighter blacks */
    --accent: 0 0% 8%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 75% 50%;
    --destructive-foreground: 0 0% 95%;

    --border: 0 0% 15%;
    --input: 0 0% 8%;
    --ring: 180 100% 50%;

    /* Humanoid design tokens - robotic gradients */
    --gradient-primary: linear-gradient(135deg, hsl(180 100% 50%), hsl(195 100% 55%));
    --gradient-secondary: linear-gradient(135deg, hsl(0 0% 8%), hsl(0 0% 12%));
    --gradient-hero: linear-gradient(135deg, hsl(180 100% 50% / 0.05), hsl(195 100% 55% / 0.03));
    --gradient-mesh: radial-gradient(circle at 20% 80%, hsl(180 100% 50% / 0.03) 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, hsl(195 100% 55% / 0.03) 0%, transparent 50%),
                     radial-gradient(circle at 40% 40%, hsl(200 100% 60% / 0.02) 0%, transparent 50%);
    --gradient-radial: radial-gradient(circle, var(--primary) 0%, transparent 70%);

    /* Humanoid shadows - deep blacks with cyan glows */
    --shadow-glow: 0 0 30px hsl(180 100% 50% / 0.4);
    --shadow-glow-lg: 0 0 50px hsl(180 100% 50% / 0.3);
    --shadow-card: 0 4px 20px hsl(0 0% 0% / 0.8);
    --shadow-elegant: 0 8px 40px hsl(0 0% 0% / 0.9);
    --shadow-professional: 0 2px 10px hsl(180 100% 50% / 0.2), 0 4px 20px hsl(180 100% 50% / 0.1);

    /* Humanoid neon effects - robotic cyan glow */
    --shadow-neon: 0 0 5px hsl(180 100% 50%), 0 0 10px hsl(180 100% 50%), 0 0 15px hsl(180 100% 50%);
    --shadow-neon-lg: 0 0 10px hsl(180 100% 50%), 0 0 20px hsl(180 100% 50%), 0 0 30px hsl(180 100% 50%);

    /* Humanoid sidebar */
    --sidebar-background: 0 0% 4%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 95%;
    --sidebar-primary-foreground: 0 0% 4%;
    --sidebar-accent: 0 0% 8%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 12%;
    --sidebar-ring: 180 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'JetBrains Mono', 'Fira Code', 'Roboto Mono', monospace, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-image: var(--gradient-mesh);
    background-attachment: fixed;
    background-size: 100% 100%;
    background-color: hsl(0 0% 3%);
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  /* Enhanced scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, hsl(var(--primary)), hsl(var(--primary-dark)));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, hsl(var(--primary-glow)), hsl(var(--primary)));
  }
}

@layer components {
  /* Enhanced gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  .bg-gradient-mesh {
    background: var(--gradient-mesh);
  }

  .bg-gradient-radial {
    background: var(--gradient-radial);
  }

  /* Advanced glow effects */
  .glow-primary {
    box-shadow: var(--shadow-glow);
  }

  .glow-neon {
    box-shadow: var(--shadow-neon);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }

  /* Enhanced transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  .transition-spring {
    transition: var(--transition-spring);
  }

  /* Advanced hover animations */
  .hover-lift {
    @apply transition-spring hover:-translate-y-3 hover:shadow-elegant;
  }

  .hover-glow {
    @apply transition-smooth hover:glow-primary;
  }

  .hover-scale {
    @apply transition-spring hover:scale-105;
  }

  .hover-neon {
    @apply transition-smooth hover:glow-neon;
  }

  .hover-float {
    @apply transition-spring;
    animation: float 6s ease-in-out infinite;
  }

  /* Enhanced animated gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary via-primary-glow to-primary bg-clip-text text-transparent;
    background-size: 300% 300%;
    animation: gradient-shift 4s ease-in-out infinite;
  }

  .gradient-text-rainbow {
    background: linear-gradient(135deg,
      hsl(221 83% 53%),
      hsl(213 94% 68%),
      hsl(204 94% 78%),
      hsl(195 100% 85%),
      hsl(221 83% 53%)
    );
    background-size: 300% 300%;
    @apply bg-clip-text text-transparent;
    animation: professional-gradient 8s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes professional-gradient {
    0%, 100% { background-position: 0% 50%; }
    33% { background-position: 100% 50%; }
    66% { background-position: 200% 50%; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Glass morphism effects - Enhanced for dark theme */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark .glass {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .dark .glass-card {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.06);
  }

  /* Magnetic hover effect */
  .magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Pulse animation */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
    }
    50% {
      box-shadow: 0 0 40px hsl(var(--primary) / 0.6), 0 0 60px hsl(var(--primary) / 0.3);
    }
  }

  /* Advanced animations */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .bounce-in {
    animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  @keyframes bounce-in {
    0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
    50% { transform: scale(1.05) rotate(2deg); }
    70% { transform: scale(0.9) rotate(-1deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
  }

  .slide-up {
    animation: slide-up 0.6s ease-out;
  }

  @keyframes slide-up {
    0% { transform: translateY(100px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  .fade-in-scale {
    animation: fade-in-scale 0.8s ease-out;
  }

  @keyframes fade-in-scale {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  /* Hover effects */
  .hover-glow-intense:hover {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.5), 0 0 60px hsl(var(--primary) / 0.3), 0 0 90px hsl(var(--primary) / 0.1);
    transform: translateY(-5px) scale(1.02);
  }

  .hover-rotate:hover {
    transform: rotate(5deg) scale(1.05);
  }

  .hover-tilt:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.02);
  }

  /* Text effects */
  .text-glow {
    text-shadow: 0 0 10px hsl(var(--primary) / 0.5), 0 0 20px hsl(var(--primary) / 0.3), 0 0 30px hsl(var(--primary) / 0.1);
  }

  .text-stroke {
    -webkit-text-stroke: 1px hsl(var(--primary));
    text-stroke: 1px hsl(var(--primary));
  }

  /* Loading animations */
  .loading-dots {
    display: inline-block;
  }

  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }

  @keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }

  /* Zoom burst effect */
  .zoom-burst {
    animation: zoom-burst 1.2s ease-in-out;
  }

  @keyframes zoom-burst {
    0% {
      transform: scale(1);
      opacity: 1;
      filter: blur(0px);
    }
    50% {
      transform: scale(8);
      opacity: 0.8;
      filter: blur(2px);
    }
    100% {
      transform: scale(20);
      opacity: 0;
      filter: blur(10px);
    }
  }

  /* Screen flash effect */
  .screen-flash {
    animation: screen-flash 0.3s ease-out;
  }

  @keyframes screen-flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(255, 255, 255, 0.1); }
    100% { background-color: transparent; }
  }

  /* Mobile performance optimizations */
  @media (max-width: 768px) {
    .gradient-text-rainbow {
      background-size: 200% 200% !important;
      animation-duration: 2s !important;
    }

    .bg-gradient-mesh {
      opacity: 0.8;
    }

    /* Reduce motion for better mobile performance */
    @media (prefers-reduced-motion: reduce) {
      .gradient-text-rainbow {
        animation: none !important;
        background: hsl(var(--primary)) !important;
        background-clip: text !important;
        -webkit-background-clip: text !important;
      }

      .pulse-glow {
        animation: none !important;
      }

      .hover-float {
        animation: none !important;
      }
    }
  }

  /* Hardware acceleration for smooth animations */
  .will-change-transform {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Enhanced loading bar animations */
  .loading-bar-glow {
    box-shadow:
      0 0 10px hsl(var(--primary) / 0.5),
      0 0 20px hsl(var(--primary) / 0.3),
      0 0 30px hsl(var(--primary) / 0.1);
  }

  .loading-shimmer {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%
    );
    animation: shimmer-loading 2s infinite linear;
  }

  @keyframes shimmer-loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .progress-pulse {
    animation: progress-pulse 1.5s ease-in-out infinite;
  }

  @keyframes progress-pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
  }

  /* Futuristic text glow */
  .text-glow-primary {
    text-shadow:
      0 0 5px hsl(var(--primary) / 0.8),
      0 0 10px hsl(var(--primary) / 0.6),
      0 0 15px hsl(var(--primary) / 0.4),
      0 0 20px hsl(var(--primary) / 0.2);
  }

  .text-scan {
    animation: text-scan 3s ease-in-out infinite;
  }

  @keyframes text-scan {
    0%, 100% {
      text-shadow: 0 0 5px hsl(var(--primary) / 0.5);
    }
    50% {
      text-shadow:
        0 0 10px hsl(var(--primary) / 0.8),
        0 0 20px hsl(var(--primary) / 0.6),
        0 0 30px hsl(var(--primary) / 0.4);
    }
  }

  /* Professional cursor styles with theme integration */
  .cursor-dot {
    background: hsl(var(--primary) / 0.7);
    border-radius: 50%;
    transition: all 0.2s ease-out;
    box-shadow: 0 0 4px hsl(var(--primary) / 0.2);
  }

  .cursor-dot.interactive {
    background: hsl(var(--primary));
    box-shadow: 0 0 12px hsl(var(--primary) / 0.4);
    transform: scale(1.3);
  }

  .cursor-ring {
    border: 1px solid hsl(var(--primary) / 0.3);
    border-radius: 50%;
    transition: all 0.2s ease-out;
  }

  .cursor-ring.interactive {
    border: 2px solid hsl(var(--primary) / 0.8);
    box-shadow: 0 0 8px hsl(var(--primary) / 0.2);
    transform: scale(1.15);
    opacity: 0.9;
  }

  /* Keep default cursor visible - professional approach */
  @media (min-width: 768px) {
    body {
      cursor: auto; /* Keep default cursor */
    }

    /* Maintain proper cursor types for better UX */
    button, a, [role="button"], [tabindex] {
      cursor: pointer;
    }

    input[type="text"], input[type="email"], input[type="password"], textarea {
      cursor: text;
    }

    /* Resize cursor for resizable elements */
    textarea {
      cursor: text;
    }

    /* Help cursor for elements with titles/tooltips */
    [title] {
      cursor: help;
    }
  }
}

/* Global responsive improvements */
/* Safe area for mobile devices */
.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Improved text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Better focus states for accessibility */
*:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Better line clamping */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive container improvements */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Touch target improvements for mobile */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved mobile spacing */
  .section-padding-mobile {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  /* Better mobile text sizing */
  .mobile-text-responsive {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Mobile container improvements */
  .mobile-container-spacing {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile feature cards optimization */
  .mobile-feature-card {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .mobile-feature-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.375rem;
  }

  /* Humanoid Theme Specific Classes */
  .humanoid-text {
    font-family: 'JetBrains Mono', 'Fira Code', 'Roboto Mono', monospace;
    letter-spacing: 0.05em;
  }

  .humanoid-glow {
    box-shadow: 0 0 20px hsl(180 100% 50% / 0.3), 0 0 40px hsl(180 100% 50% / 0.1);
  }

  .humanoid-border {
    border: 1px solid hsl(180 100% 50% / 0.3);
    background: linear-gradient(135deg, hsl(0 0% 6% / 0.8), hsl(0 0% 8% / 0.8));
  }

  .humanoid-card {
    background: linear-gradient(135deg, hsl(0 0% 6%), hsl(0 0% 4%));
    border: 1px solid hsl(0 0% 15%);
    box-shadow: 0 4px 20px hsl(0 0% 0% / 0.8), inset 0 1px 0 hsl(0 0% 20% / 0.1);
  }

  .humanoid-button {
    background: linear-gradient(135deg, hsl(180 100% 50%), hsl(195 100% 55%));
    border: none;
    color: hsl(0 0% 3%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
  }

  .humanoid-button:hover {
    background: linear-gradient(135deg, hsl(180 100% 60%), hsl(195 100% 65%));
    box-shadow: 0 0 20px hsl(180 100% 50% / 0.5);
    transform: translateY(-2px);
  }

  .humanoid-outline-button {
    background: transparent;
    border: 2px solid hsl(180 100% 50%);
    color: hsl(180 100% 50%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
  }

  .humanoid-outline-button:hover {
    background: hsl(180 100% 50% / 0.1);
    box-shadow: 0 0 20px hsl(180 100% 50% / 0.3);
    transform: translateY(-2px);
  }

  .humanoid-grid-pattern {
    background-image:
      linear-gradient(hsl(0 0% 15% / 0.1) 1px, transparent 1px),
      linear-gradient(90deg, hsl(0 0% 15% / 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .mobile-feature-text {
    font-size: 0.75rem;
    line-height: 1.2;
  }
}