import { motion } from "framer-motion";

const VisionSection = () => {
  return (
    <section id="vision" className="py-12 sm:py-16 lg:py-20 xl:py-24 relative">
      <div className="absolute inset-0 bg-gradient-hero"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-5xl mx-auto text-center space-y-6 sm:space-y-8 lg:space-y-12 xl:space-y-16">
          {/* Section Header */}
          <motion.div
            className="space-y-3 sm:space-y-4 lg:space-y-6"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.h2
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight humanoid-text"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              THE <span className="gradient-text">FUTURE</span>
            </motion.h2>
            <motion.p
              className="text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto px-2 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              A world where humanoid AI entities seamlessly integrate into human society,
              enhancing our capabilities while preserving our humanity. The convergence of
              consciousness and technology creates unprecedented possibilities.
            </motion.p>
          </motion.div>

          {/* Mission Statement */}
          <motion.div
            className="humanoid-card rounded-xl sm:rounded-2xl lg:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 backdrop-blur-sm shadow-card hover-lift humanoid-glow"
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ scale: 1.02, y: -5 }}
          >
            <div className="space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8">
              <motion.h3
                className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-center humanoid-text"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
                viewport={{ once: true }}
              >
                NEURAL NEXUS PROTOCOL
              </motion.h3>
              <motion.p
                className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-muted-foreground leading-relaxed text-center max-w-4xl mx-auto px-2 sm:px-0"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
                viewport={{ once: true }}
              >
                Developing advanced neural architectures that bridge the gap between artificial and
                human intelligence. Our humanoid AI systems will possess adaptive consciousness,
                emotional understanding, and seamless integration capabilities, creating the first
                generation of truly sentient artificial beings designed to coexist and collaborate
                with humanity in perfect harmony.
              </motion.p>
            </div>
          </motion.div>

          {/* Timeline */}
          <div></div>
        </div>
      </div>
    </section>
  );
};

export default VisionSection;