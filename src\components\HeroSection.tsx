import { But<PERSON> } from "@/components/ui/button";
import { FloatingParticles } from "@/components/ui/floating-particles";
import { TextReveal } from "@/components/ui/text-reveal";
import { ArrowRight } from "lucide-react";
import Model3D from "./3d_models/Model3D";
import { motion, useScroll, useTransform } from "framer-motion";

const HeroSection = () => {
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, 50]);

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Full Screen 3D Model Background */}
      <Model3D />

      {/* Professional Floating Particles */}
      <FloatingParticles count={12} className="opacity-20" />

      {/* Subtle Professional Orbs - Reduced opacity for overlay */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-48 h-48 sm:w-64 sm:h-64 bg-primary/4 rounded-full blur-3xl"
        style={{ y: y1 }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-48 h-48 sm:w-64 sm:h-64 bg-primary/3 rounded-full blur-3xl"
        style={{ y: y2 }}
        animate={{
          scale: [1, 1.05, 1],
          opacity: [0.1, 0.15, 0.1]
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Minimal ambient orb */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-24 h-24 sm:w-32 sm:h-32 bg-primary/3 rounded-full blur-2xl"
        animate={{
          x: [-10, 10, -10],
          y: [-5, 5, -5],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-20 pt-16 sm:pt-20 lg:pt-24">
        <div className="max-w-7xl mx-auto">
          {/* Centered Content Overlay */}
          <div className="flex flex-col items-center justify-center text-center space-y-6 lg:space-y-8">
            {/* Professional Main Heading */}
            <motion.div
              className="space-y-4 lg:space-y-6"
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight tracking-tight text-white drop-shadow-2xl">
                <TextReveal variant="slide" delay={0.2}>
                  Advanced AI Solutions for
                </TextReveal>{" "}
                <motion.span
                  className="gradient-text block sm:inline"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
                >
                  Enterprise Innovation
                </motion.span>
              </h1>
            </motion.div>

            {/* Professional Description */}
            <motion.div
              className="space-y-6 lg:space-y-8 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
            >
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-white/90 leading-relaxed drop-shadow-lg">
                Pioneering next-generation artificial intelligence and robotics solutions
                that transform industries and accelerate business growth worldwide.
              </p>

              {/* Professional CTA Buttons */}
              <motion.div
                className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4 lg:pt-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
              >
                <Button
                  size="lg"
                  className="w-full sm:w-auto bg-primary hover:bg-primary/90 shadow-professional px-6 sm:px-8 py-3 text-sm sm:text-base font-semibold"
                >
                  Explore Solutions
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto border-white/30 hover:border-white/50 text-white hover:text-white hover:bg-white/10 px-6 sm:px-8 py-3 text-sm sm:text-base font-semibold backdrop-blur-sm"
                >
                  Contact Sales
                </Button>
              </motion.div>
            </motion.div>
          </div>


          {/* Professional Stats */}
          <motion.div
            className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 pt-12 sm:pt-16 lg:pt-20 max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
          >
            {[
              { value: "99.9%", label: "Uptime SLA" },
              { value: "50+", label: "Enterprise Clients" },
              { value: "24/7", label: "Global Support" },
              { value: "ISO", label: "Certified" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center space-y-1 sm:space-y-2 p-4 sm:p-6 rounded-xl bg-card/30 border border-border/20 hover:border-primary/20 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.9 + index * 0.1, ease: "easeOut" }}
                whileHover={{ scale: 1.02, y: -2 }}
              >
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary">{stat.value}</div>
                <div className="text-xs sm:text-sm text-muted-foreground font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;