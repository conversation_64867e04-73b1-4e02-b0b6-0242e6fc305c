import { EnhancedCard } from "@/components/ui/enhanced-card";
import { TextReveal } from "@/components/ui/text-reveal";
import { <PERSON><PERSON>, <PERSON>pu, Zap, Target } from "lucide-react";
import { motion } from "framer-motion";

const AboutSection = () => {
  const features = [
    {
      icon: Bo<PERSON>,
      title: "Humanoid Robotics",
      description: "Advanced humanoid systems designed for real-world applications and human interaction."
    },
    {
      icon: Cpu,
      title: "Physical AI Systems",
      description: "Cutting-edge AI integrated with hardware for intelligent robotic behavior."
    },
    {
      icon: Zap,
      title: "General Purpose",
      description: "Versatile robots capable of performing multiple tasks across various industries."
    },
    {
      icon: Target,
      title: "Commercial Scale",
      description: "Built for mass production and deployment in commercial applications."
    }
  ];

  return (
    <section id="about" className="py-8 sm:py-12 lg:py-16 xl:py-20 relative">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        {/* Mobile: Title and Description First */}
        <motion.div
          className="lg:hidden text-center space-y-3 mb-6"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl sm:text-3xl font-bold leading-tight">
            <TextReveal variant="wave">Who</TextReveal>{" "}
            <TextReveal variant="wave">We</TextReveal>{" "}
            <span className="gradient-text">Are</span>
          </h2>
          <p className="text-sm sm:text-base text-muted-foreground leading-relaxed px-1">
            xSpecies AI is at the forefront of robotics innovation, combining cutting-edge AI
            with advanced hardware systems to create the next generation of general purpose
            robots and humanoids.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 xl:gap-12 items-center">
          {/* Left Content - Desktop Only */}
          <motion.div
            className="hidden lg:block space-y-6 xl:space-y-8 order-2 lg:order-1"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div
              className="space-y-4 xl:space-y-6"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
                <TextReveal variant="wave">Who</TextReveal>{" "}
                <TextReveal variant="wave">We</TextReveal>{" "}
                <span className="gradient-text">Are</span>
              </h2>
              <p className="text-lg lg:text-xl text-muted-foreground leading-relaxed">
                xSpecies AI is at the forefront of robotics innovation, combining cutting-edge AI
                with advanced hardware systems to create the next generation of general purpose
                robots and humanoids.
              </p>
            </motion.div>

            {/* Features Grid - Desktop */}
            <motion.div
              className="grid grid-cols-2 gap-4 xl:gap-6"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                >
                  <EnhancedCard
                    variant="glass"
                    tilt
                    glow
                    className="flex items-start space-x-3 lg:space-x-4 p-3 lg:p-4 xl:p-5 h-full humanoid-card"
                  >
                    <motion.div
                      className="w-8 h-8 lg:w-10 lg:h-10 xl:w-12 xl:h-12 rounded-lg bg-gradient-primary flex items-center justify-center shrink-0 shadow-lg humanoid-glow"
                      whileHover={{ rotate: 5, scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <feature.icon className="w-4 h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6 text-primary-foreground" />
                    </motion.div>
                    <div className="space-y-1 lg:space-y-2 min-w-0 flex-1">
                      <h3 className="font-semibold text-sm lg:text-base xl:text-lg text-foreground leading-tight">{feature.title}</h3>
                      <p className="text-sm lg:text-base text-muted-foreground leading-relaxed">{feature.description}</p>
                    </div>
                  </EnhancedCard>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Image */}
          <motion.div
            className="relative order-1 lg:order-2"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div
              className="aspect-[4/3] w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mx-auto rounded-xl sm:rounded-2xl lg:rounded-3xl bg-gradient-secondary border border-border shadow-elegant overflow-hidden hover-lift"
              whileHover={{ scale: 1.02, rotateY: 5 }}
              transition={{ duration: 0.3 }}
            >
              <img
                src="/lovable-uploads/902feb7f-1aec-415b-842f-d82b3b95a6b1.png"
                alt="Humanoid Robot - Next Generation AI"
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </motion.div>
          </motion.div>
        </div>

        {/* Mobile: Features Grid Below Image */}
        <motion.div
          className="lg:hidden mt-6"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 + index * 0.1, ease: "easeOut" }}
                viewport={{ once: true }}
              >
                <EnhancedCard
                  variant="glass"
                  tilt
                  glow
                  className="flex flex-col items-center space-y-2 p-2 sm:p-3 h-full text-center"
                >
                  <motion.div
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-md sm:rounded-lg bg-gradient-primary flex items-center justify-center shrink-0 shadow-lg"
                    whileHover={{ rotate: 5, scale: 1.1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <feature.icon className="w-3 h-3 sm:w-4 sm:h-4 text-primary-foreground" />
                  </motion.div>
                  <div className="space-y-0.5 min-w-0 flex-1">
                    <h3 className="font-semibold text-xs sm:text-sm text-foreground leading-tight">{feature.title}</h3>
                    <p className="text-xs sm:text-sm text-muted-foreground leading-snug">{feature.description}</p>
                  </div>
                </EnhancedCard>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;