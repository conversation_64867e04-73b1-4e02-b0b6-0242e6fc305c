// Full screen Spline 3D Model component for hero background
const Model3D = () => {
  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Spline 3D Model Embed - Full Screen */}
      <iframe
        src='https://my.spline.design/nexbotrobotcharacterconcept-Ck0wH1GvI4lMgScsQVKwqtHg/'
        width='100%'
        height='100%'
        className="w-full h-full object-cover border-none"
        title="Neural Nexus 3D Robot Character"
        loading="lazy"
        style={{ border: 'none' }}
      />

      {/* Overlay to ensure text readability */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-[0.5px]"></div>
    </div>
  );
};

export default Model3D;
