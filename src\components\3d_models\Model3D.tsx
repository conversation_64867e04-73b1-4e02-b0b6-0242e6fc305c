// Spline 3D Model component
const Model3D = () => {

  return (
    <div className="w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem] relative">
      {/* Glow effect background */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-glow/20 rounded-full blur-3xl"></div>

      {/* Spline 3D Model Embed */}
      <div className="relative w-full h-full rounded-lg overflow-hidden">
        <iframe
          src='https://my.spline.design/nexbotrobotcharacterconcept-Ck0wH1GvI4lMgScsQVKwqtHg/'
          width='100%'
          height='100%'
          className="w-full h-full object-cover border-none rounded-lg"
          title="xSpecies AI 3D Robot Character"
          loading="lazy"
          style={{ border: 'none' }}
        />
      </div>
    </div>
  );
};

export default Model3D;
